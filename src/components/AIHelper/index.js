/*
 * @file 直播小助手
 * <AUTHOR>
 * @date 2025-07-25 11:07:22
 */

import React, {useState, useRef, useEffect} from 'react';
import classNames from 'classnames';
import './index.less';

const InnerComp = ({
    sections = [],
    bottomCard = {},
    questions = [],
    onConsult = () => {},
    onQuestionClick = () => {}
}) => {
    const [activeTab, setActiveTab] = useState(0);
    const [isBottomCardVisible, setIsBottomCardVisible] = useState(true);
    const containerRef = useRef(null);
    const sectionRefs = useRef([]);
    const tabsRef = useRef(null);

    // 默认数据
    const defaultSections = [
        {
            id: 1,
            title: '白癜风症状全解析',
            content: '皮肤出现不明原因的白斑？可能是白癜风在作祟！白癜风初期表现为米粒至指甲大小的白色斑片，逐渐发展为瓷白色斑块，常出现在手指、口角等暴露部位。部分患者还会伴随局部瘙痒、毛发变白等症状。',
            videoUrl: 'https://example.com/video1.mp4',
            videoCover: 'https://example.com/cover1.jpg',
            videoDuration: '03:48'
        },
        {
            id: 2,
            title: '白癜风8大诱因揭秘',
            content: '了解白癜风的诱发因素，有助于预防和治疗。主要包括：遗传因素、免疫系统异常、精神压力、外伤刺激、化学物质接触、内分泌失调、微量元素缺乏、感染等8大因素。',
            videoUrl: 'https://example.com/video2.mp4',
            videoCover: 'https://example.com/cover2.jpg',
            videoDuration: '04:12'
        },
        {
            id: 3,
            title: '白癜风治疗方案',
            content: '白癜风治疗需要个性化方案，常见治疗方法包括：药物治疗（外用激素、免疫调节剂）、光疗（308激光、UVB）、手术治疗（自体表皮移植）等。早期治疗效果更佳，建议及时就医。',
            videoUrl: 'https://example.com/video3.mp4',
            videoCover: 'https://example.com/cover3.jpg',
            videoDuration: '05:20'
        }
    ];

    const defaultBottomCard = {
        icon: 'https://example.com/doctor-icon.jpg',
        title: '领取白癜风修复白斑中药方',
        subtitle: '1000人已预约',
        buttonText: '立即咨询'
    };

    // 默认问题数据
    const defaultQuestions = [
        {
            id: 1,
            text: '白癜风白斑和普通白斑有什么区别吗？',
            isHighlighted: false
        },
        {
            id: 2,
            text: '白癜风的白斑会自己消失吗',
            isHighlighted: true
        },
        {
            id: 3,
            text: '白斑发展速度快吗？多久会扩散？',
            isHighlighted: false
        },
        {
            id: 4,
            text: '我想问其他问题',
            isHighlighted: false,
            isOther: true
        }
    ];

    const finalSections = sections.length > 0 ? sections : defaultSections;
    const finalBottomCard = Object.keys(bottomCard).length > 0 ? bottomCard : defaultBottomCard;
    const finalQuestions = questions.length > 0 ? questions : defaultQuestions;

    // 处理tab点击
    const handleTabClick = (index) => {
        setActiveTab(index);
        // 滚动到对应section
        if (sectionRefs.current[index]) {
            const offsetTop = sectionRefs.current[index].offsetTop;
            // 考虑tab栏高度
            const tabsHeight = tabsRef.current ? tabsRef.current.offsetHeight : 0;
            containerRef.current.scrollTo({
                top: offsetTop - tabsHeight - 20, // 额外20px间距
                behavior: 'smooth'
            });
        }
    };

    // 处理滚动事件，更新active tab
    const handleScroll = () => {
        if (!containerRef.current || !sectionRefs.current.length) {
            return;
        }

        const scrollTop = containerRef.current.scrollTop;
        const tabsHeight = tabsRef.current ? tabsRef.current.offsetHeight : 0;
        const containerHeight = containerRef.current.clientHeight;
        const scrollHeight = containerRef.current.scrollHeight;

        // 检查是否接近底部（距离底部100px以内）
        const isNearBottom = scrollTop + containerHeight >= scrollHeight - 100;

        // 如果是q2c环境，距离底部100px时隐藏底部卡片
        const isQ2C = window.location.search.includes('q2c') || window.location.pathname.includes('q2c');
        if (isQ2C && isNearBottom) {
            setIsBottomCardVisible(false);
        }
        else {
            setIsBottomCardVisible(true);
        }

        // 计算当前应该激活的tab
        let newActiveTab = 0;
        for (let i = sectionRefs.current.length - 1; i >= 0; i--) {
            const section = sectionRefs.current[i];
            if (section && scrollTop + tabsHeight + 50 >= section.offsetTop) {
                newActiveTab = i;
                break;
            }
        }

        if (newActiveTab !== activeTab) {
            setActiveTab(newActiveTab);
        }
    };

    useEffect(() => {
        const container = containerRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);
            return () => container.removeEventListener('scroll', handleScroll);
        }
    }, [activeTab]);

    // 视频播放处理
    const handleVideoPlay = (videoUrl) => {
        // 这里可以集成具体的视频播放逻辑
        console.log('播放视频:', videoUrl);
    };

    return (
        <div className="medical-popular-science">
            {/* 固定的tab栏 */}
            <div className="tabs-container" ref={tabsRef}>
                <div className="tabs-wrapper">
                    <div className="main-title">讲解回放</div>
                    <div className="tabs">
                        {finalSections.map((section, index) => (
                            <div
                                key={section.id}
                                className={classNames('tab-item', {
                                    active: activeTab === index
                                })}
                                onClick={() => handleTabClick(index)}
                            >
                                {section.title}
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* 滚动内容区域 */}
            <div className="content-container" ref={containerRef}>
                {finalSections.map((section, index) => (
                    <div
                        key={section.id}
                        className="content-section"
                        ref={el => sectionRefs.current[index] = el}
                    >
                        <div className="section-content">
                            <p className="content-text">{section.content}</p>

                            <div className="video-container">
                                <div
                                    className="video-wrapper"
                                    onClick={() => handleVideoPlay(section.videoUrl)}
                                >
                                    <img
                                        src={section.videoCover}
                                        alt="视频封面"
                                        className="video-cover"
                                    />
                                    <div className="play-button">
                                        <div className="play-icon"></div>
                                    </div>
                                    <div className="video-duration">
                                        {section.videoDuration}
                                    </div>
                                </div>
                            </div>

                            {index === finalSections.length - 1 && (
                                <div className="questions-section">
                                    <div className="question-prompt">
                                        猜你想问：
                                    </div>
                                    <div className="questions-list">
                                        {finalQuestions.map((question) => (
                                            <div
                                                key={question.id}
                                                className={classNames('question-item', {
                                                    highlighted: question.isHighlighted,
                                                    other: question.isOther
                                                })}
                                                onClick={() => onQuestionClick(question)}
                                            >
                                                {question.text}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            {/* 底部吸底卡片 */}
            {isBottomCardVisible && (
                <div className="bottom-card">
                    <div className="card-content">
                        <div className="card-left">
                            <img src={finalBottomCard.icon} alt="医生头像" className="doctor-icon" />
                            <div className="card-info">
                                <div className="card-title">{finalBottomCard.title}</div>
                                <div className="card-subtitle">{finalBottomCard.subtitle}</div>
                            </div>
                        </div>
                        <button className="consult-button" onClick={onConsult}>
                            {finalBottomCard.buttonText}
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};


export default function AIHelper() {
    const sections = [
        {
            id: 1,
            title: '白癜风症状全解析',
            content: '皮肤出现不明原因的白斑？可能是白癜风在作祟！白癜风初期表现为米粒至指甲大小的白色斑片，逐渐发展为瓷白色斑块，常出现在手指、口角等暴露部位。部分患者还会伴随局部瘙痒、毛发变白等症状。',
            videoUrl: 'https://example.com/video1.mp4',
            videoCover: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg',
            videoDuration: '03:48'
        },
        {
            id: 2,
            title: '白癜风8大诱因揭秘',
            content: '了解白癜风的诱发因素，有助于预防和治疗。主要包括：遗传因素、免疫系统异常、精神压力、外伤刺激、化学物质接触、内分泌失调、微量元素缺乏、感染等8大因素。这些因素可能单独作用，也可能相互影响，共同导致白癜风的发生。',
            videoUrl: 'https://example.com/video2.mp4',
            videoCover: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg',
            videoDuration: '04:12'
        },
        {
            id: 3,
            title: '白癜风治疗方案',
            content: '白癜风治疗需要个性化方案，常见治疗方法包括：药物治疗（外用激素、免疫调节剂）、光疗（308激光、UVB）、手术治疗（自体表皮移植）等。早期治疗效果更佳，建议及时就医。治疗过程中需要保持耐心，配合医生制定的治疗计划。',
            videoUrl: 'https://example.com/video3.mp4',
            videoCover: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg',
            videoDuration: '05:20'
        }
    ];

    const bottomCard = {
        icon: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg',
        title: '领取白癜风修复白斑中药方',
        subtitle: '1000人已预约',
        buttonText: '立即咨询'
    };

    // 问题数据
    const questions = [
        {
            id: 1,
            text: '白癜风白斑和普通白斑有什么区别吗？',
            isHighlighted: false
        },
        {
            id: 2,
            text: '白癜风的白斑会自己消失吗',
            isHighlighted: true
        },
        {
            id: 3,
            text: '白斑发展速度快吗？多久会扩散？',
            isHighlighted: false
        },
        {
            id: 4,
            text: '我想问其他问题',
            isHighlighted: false,
            isOther: true
        }
    ];

    // 咨询按钮点击处理
    const handleConsult = () => {
        console.log('点击咨询按钮');
    };

    // 问题点击处理
    const handleQuestionClick = (question) => {
        console.log('点击问题:', question.text);
        // 这里可以添加具体的问题处理逻辑，比如跳转到咨询页面或显示答案
    };

    return (
        <div style={{height: '100vh', background: '#f5f5f5'}}>
            <InnerComp
                sections={sections}
                bottomCard={bottomCard}
                questions={questions}
                onConsult={handleConsult}
                onQuestionClick={handleQuestionClick}
            />
        </div>
    );
}
