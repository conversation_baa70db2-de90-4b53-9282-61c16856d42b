/*
 * @file AIHelper less file
 * <AUTHOR> Assistant
 * @date 2025-01-25
 */
@import (reference) './style-util.less';

// 1rem = 100px
.medical-popular-science {
    position: relative;
    width: 100%;
    height: 100vh;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // 固定的tab栏
    .tabs-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background: #fff;
        border-bottom: .01rem solid #e5e5e5;

        .tabs-wrapper {
            padding: .2rem .16rem .16rem;

            .main-title {
                font-size: .18rem;
                font-weight: 600;
                color: #333;
                text-align: center;
                margin-bottom: .16rem;
                line-height: .25rem;
            }

            .tabs {
                display: flex;
                justify-content: space-between;
                gap: .08rem;

                .tab-item {
                    flex: 1;
                    padding: .08rem .12rem;
                    font-size: .14rem;
                    color: #666;
                    text-align: center;
                    border: .01rem solid #e5e5e5;
                    border-radius: .04rem;
                    background: #fff;
                    cursor: pointer;
                    transition: all .3s ease;
                    line-height: .2rem;
                    .text-ellipse(1);

                    &.active {
                        color: #ff4d4d;
                        border-color: #ff4d4d;
                        background: rgba(255, 77, 77, .05);
                    }

                    &:hover {
                        border-color: #ff4d4d;
                    }
                }
            }
        }
    }

    // 滚动内容区域
    .content-container {
        flex: 1;
        overflow-y: auto;
        padding-top: 1.2rem; // 为固定tab栏留出空间
        padding-bottom: 1rem; // 为底部卡片留出空间
        -webkit-overflow-scrolling: touch;

        .content-section {
            padding: .2rem .16rem;

            .section-content {
                .content-text {
                    font-size: .16rem;
                    line-height: .24rem;
                    color: #333;
                    margin: 0 0 .16rem 0;
                    text-align: justify;
                }

                .video-container {
                    margin-bottom: .2rem;

                    .video-wrapper {
                        position: relative;
                        width: 100%;
                        height: 2.4rem;
                        border-radius: .08rem;
                        overflow: hidden;
                        cursor: pointer;
                        box-shadow: 0 .02rem .08rem rgba(0, 0, 0, .1);

                        .video-cover {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            display: block;
                        }

                        .play-button {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            width: .6rem;
                            height: .6rem;
                            background: rgba(0, 0, 0, .6);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all .3s ease;

                            &:hover {
                                background: rgba(0, 0, 0, .8);
                                transform: translate(-50%, -50%) scale(1.1);
                            }

                            .play-icon {
                                width: 0;
                                height: 0;
                                border-left: .16rem solid #fff;
                                border-top: .1rem solid transparent;
                                border-bottom: .1rem solid transparent;
                                margin-left: .02rem;
                            }
                        }

                        .video-duration {
                            position: absolute;
                            bottom: .08rem;
                            right: .08rem;
                            background: rgba(0, 0, 0, .7);
                            color: #fff;
                            font-size: .12rem;
                            padding: .02rem .06rem;
                            border-radius: .02rem;
                            line-height: .16rem;
                        }
                    }
                }

                // 猜你想问组件
                .questions-section {
                    margin-top: .2rem;

                    .question-prompt {
                        font-size: .14rem;
                        color: #666;
                        margin-bottom: .12rem;
                        line-height: .2rem;
                    }

                    .questions-list {
                        display: flex;
                        flex-direction: column;
                        gap: .08rem;

                        .question-item {
                            padding: .12rem .16rem;
                            background: #f8f8f8;
                            border: .01rem solid #e5e5e5;
                            border-radius: .06rem;
                            font-size: .14rem;
                            color: #333;
                            line-height: .2rem;
                            cursor: pointer;
                            transition: all .3s ease;
                            position: relative;

                            &:hover {
                                background: #f0f0f0;
                                border-color: #d0d0d0;
                            }

                            &:active {
                                background: #e8e8e8;
                            }

                            // 高亮问题样式（红色边框）
                            &.highlighted {
                                border-color: #ff4d4d;
                                background: rgba(255, 77, 77, .02);

                                &::after {
                                    content: '';
                                    position: absolute;
                                    right: .12rem;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    width: .06rem;
                                    height: .06rem;
                                    background: #ff4d4d;
                                    border-radius: 50%;
                                }

                                &:hover {
                                    background: rgba(255, 77, 77, .05);
                                    border-color: #ff3333;
                                }
                            }

                            // "我想问其他问题"样式（蓝色边框）
                            &.other {
                                border-color: #4285f4;
                                background: rgba(66, 133, 244, .02);
                                color: #4285f4;

                                &:hover {
                                    background: rgba(66, 133, 244, .05);
                                    border-color: #3367d6;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 底部吸底卡片
    .bottom-card {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 99;
        background: #fff;
        border-top: .01rem solid #e5e5e5;
        padding: .16rem;
        box-shadow: 0 -.02rem .08rem rgba(0, 0, 0, .1);

        .card-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: .12rem;

            .card-left {
                display: flex;
                align-items: center;
                flex: 1;
                gap: .12rem;

                .doctor-icon {
                    width: .48rem;
                    height: .48rem;
                    border-radius: 50%;
                    object-fit: cover;
                    flex-shrink: 0;
                }

                .card-info {
                    flex: 1;
                    min-width: 0;

                    .card-title {
                        font-size: .16rem;
                        font-weight: 500;
                        color: #333;
                        line-height: .22rem;
                        margin-bottom: .04rem;
                        .text-ellipse(1);
                    }

                    .card-subtitle {
                        font-size: .12rem;
                        color: #ff4d4d;
                        line-height: .17rem;
                        .text-ellipse(1);
                    }
                }
            }

            .consult-button {
                flex-shrink: 0;
                padding: .1rem .24rem;
                background: linear-gradient(135deg, #ff4d4d 0%, #ff1f66 100%);
                color: #fff;
                font-size: .14rem;
                font-weight: 500;
                border: none;
                border-radius: .22rem;
                cursor: pointer;
                transition: all .3s ease;
                line-height: .2rem;
                min-width: .88rem;
                text-align: center;

                &:hover {
                    background: linear-gradient(135deg, #f33 0%, #ff0052 100%);
                    transform: translateY(-.01rem);
                    box-shadow: 0 .04rem .12rem rgba(255, 77, 77, .3);
                }

                &:active {
                    transform: translateY(0);
                    box-shadow: 0 .02rem .06rem rgba(255, 77, 77, .2);
                }
            }
        }
    }
}
